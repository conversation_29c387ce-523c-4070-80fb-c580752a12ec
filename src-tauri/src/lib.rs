use screenshots::Screen;
use image::{<PERSON>B<PERSON>er, RgbaImage, DynamicImage};
use std::path::Path;
use std::fs;
use chrono::{DateTime, Local};
use serde::{Deserialize, Serialize};
use tauri_plugin_clipboard_manager::ClipboardExt;
use tauri::image::Image;

#[derive(Debug, Serialize, Deserialize)]
pub struct ScreenshotConfig {
    pub save_path: String,
    pub filename_pattern: String,
    pub format: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CaptureRegion {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScreenshotResult {
    pub success: bool,
    pub file_path: Option<String>,
    pub error: Option<String>,
}

fn generate_filename(pattern: &str) -> String {
    let now: DateTime<Local> = Local::now();
    pattern
        .replace("{timestamp}", &now.timestamp().to_string())
        .replace("{date}", &now.format("%Y-%m-%d").to_string())
        .replace("{time}", &now.format("%H-%M-%S").to_string())
        .replace("{datetime}", &now.format("%Y-%m-%d_%H-%M-%S").to_string())
        .replace("{year}", &now.format("%Y").to_string())
        .replace("{month}", &now.format("%m").to_string())
        .replace("{day}", &now.format("%d").to_string())
        .replace("{hour}", &now.format("%H").to_string())
        .replace("{minute}", &now.format("%M").to_string())
        .replace("{second}", &now.format("%S").to_string())
}

fn ensure_directory_exists(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let dir_path = Path::new(path);
    if !dir_path.exists() {
        fs::create_dir_all(dir_path)?;
    }
    Ok(())
}

fn save_image_to_file(image: &DynamicImage, file_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    image.save(file_path)?;
    Ok(())
}



#[tauri::command]
fn get_default_save_directory() -> Result<String, String> {
    #[cfg(target_os = "windows")]
    {
        match std::env::var("USERPROFILE") {
            Ok(profile) => Ok(format!("{}\\Desktop", profile)),
            Err(_) => Ok("C:\\Users\\<USER>\\Desktop".to_string()),
        }
    }

    #[cfg(target_os = "macos")]
    {
        match std::env::var("HOME") {
            Ok(home) => Ok(format!("{}/Desktop", home)),
            Err(_) => Ok("/Users/<USER>".to_string()),
        }
    }

    #[cfg(target_os = "linux")]
    {
        match std::env::var("HOME") {
            Ok(home) => {
                // Try XDG Desktop directory first
                let xdg_desktop = format!("{}/Desktop", home);
                if Path::new(&xdg_desktop).exists() {
                    Ok(xdg_desktop)
                } else {
                    Ok(home)
                }
            },
            Err(_) => Ok("/tmp".to_string()),
        }
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        Ok(".".to_string())
    }
}

#[tauri::command]
async fn capture_fullscreen(app: tauri::AppHandle, config: ScreenshotConfig) -> Result<ScreenshotResult, String> {
    match Screen::all() {
        Ok(screens) => {
            if let Some(screen) = screens.first() {
                match screen.capture() {
                    Ok(image) => {
                        // Ensure directory exists
                        if let Err(e) = ensure_directory_exists(&config.save_path) {
                            return Ok(ScreenshotResult {
                                success: false,
                                file_path: None,
                                error: Some(format!("Failed to create directory: {}", e)),
                            });
                        }

                        let filename = generate_filename(&config.filename_pattern);
                        let file_path = format!("{}/{}.{}", config.save_path, filename, config.format);

                        // Convert screenshots RgbaImage to image crate RgbaImage
                        let rgba_image: RgbaImage = ImageBuffer::from_raw(
                            image.width(),
                            image.height(),
                            image.as_raw().clone(),
                        ).ok_or("Failed to create image buffer")?;

                        let dynamic_image = DynamicImage::ImageRgba8(rgba_image);

                        // Save to file
                        match save_image_to_file(&dynamic_image, &file_path) {
                            Ok(_) => {
                                // Copy to clipboard - create Tauri Image from the screenshot data
                                let tauri_image = Image::new(image.as_raw(), image.width(), image.height());
                                let _ = app.clipboard().write_image(&tauri_image);

                                Ok(ScreenshotResult {
                                    success: true,
                                    file_path: Some(file_path),
                                    error: None,
                                })
                            },
                            Err(e) => Ok(ScreenshotResult {
                                success: false,
                                file_path: None,
                                error: Some(format!("Failed to save image: {}", e)),
                            }),
                        }
                    }
                    Err(e) => Ok(ScreenshotResult {
                        success: false,
                        file_path: None,
                        error: Some(format!("Failed to capture screen: {}", e)),
                    }),
                }
            } else {
                Ok(ScreenshotResult {
                    success: false,
                    file_path: None,
                    error: Some("No screens found".to_string()),
                })
            }
        }
        Err(e) => Ok(ScreenshotResult {
            success: false,
            file_path: None,
            error: Some(format!("Failed to get screens: {}", e)),
        }),
    }
}

#[tauri::command]
async fn capture_region(app: tauri::AppHandle, region: CaptureRegion, config: ScreenshotConfig) -> Result<ScreenshotResult, String> {
    match Screen::all() {
        Ok(screens) => {
            if let Some(screen) = screens.first() {
                match screen.capture_area(region.x, region.y, region.width, region.height) {
                    Ok(image) => {
                        // Ensure directory exists
                        if let Err(e) = ensure_directory_exists(&config.save_path) {
                            return Ok(ScreenshotResult {
                                success: false,
                                file_path: None,
                                error: Some(format!("Failed to create directory: {}", e)),
                            });
                        }

                        let filename = generate_filename(&config.filename_pattern);
                        let file_path = format!("{}/{}.{}", config.save_path, filename, config.format);

                        // Convert screenshots RgbaImage to image crate RgbaImage
                        let rgba_image: RgbaImage = ImageBuffer::from_raw(
                            image.width(),
                            image.height(),
                            image.as_raw().clone(),
                        ).ok_or("Failed to create image buffer")?;

                        let dynamic_image = DynamicImage::ImageRgba8(rgba_image);

                        // Save to file
                        match save_image_to_file(&dynamic_image, &file_path) {
                            Ok(_) => {
                                // Copy to clipboard - create Tauri Image from the screenshot data
                                let tauri_image = Image::new(image.as_raw(), image.width(), image.height());
                                let _ = app.clipboard().write_image(&tauri_image);

                                Ok(ScreenshotResult {
                                    success: true,
                                    file_path: Some(file_path),
                                    error: None,
                                })
                            },
                            Err(e) => Ok(ScreenshotResult {
                                success: false,
                                file_path: None,
                                error: Some(format!("Failed to save image: {}", e)),
                            }),
                        }
                    }
                    Err(e) => Ok(ScreenshotResult {
                        success: false,
                        file_path: None,
                        error: Some(format!("Failed to capture region: {}", e)),
                    }),
                }
            } else {
                Ok(ScreenshotResult {
                    success: false,
                    file_path: None,
                    error: Some("No screens found".to_string()),
                })
            }
        }
        Err(e) => Ok(ScreenshotResult {
            success: false,
            file_path: None,
            error: Some(format!("Failed to get screens: {}", e)),
        }),
    }
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            capture_fullscreen,
            capture_region,
            get_default_save_directory
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
