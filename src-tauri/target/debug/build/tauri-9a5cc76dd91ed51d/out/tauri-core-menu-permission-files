["/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/append.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/create_default.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/get.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/insert.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/is_checked.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/is_enabled.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/items.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/new.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/popup.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/prepend.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/remove.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/remove_at.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_accelerator.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_as_app_menu.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_as_help_menu_for_nsapp.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_as_window_menu.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_as_windows_menu_for_nsapp.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_checked.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_enabled.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_icon.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/set_text.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/commands/text.toml", "/Users/<USER>/code/screenshot_rs/src-tauri/target/debug/build/tauri-9a5cc76dd91ed51d/out/permissions/menu/autogenerated/default.toml"]