{"rustc": 12610991425282158916, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 13591308866259334521, "deps": [[654232091421095663, "tauri_utils", false, 15120998868653369841], [2704937418414716471, "tauri_codegen", false, 3295113909884773670], [3060637413840920116, "proc_macro2", false, 5643294594616372651], [4974441333307933176, "syn", false, 2207489294094183346], [13077543566650298139, "heck", false, 6788684706634904386], [17990358020177143287, "quote", false, 5791034238459340517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-41e0342160076486/dep-lib-tauri_macros", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}