{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 69793902400042306, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9282436027256527153], [3150220818285335163, "url", false, 9822936431616254700], [3191507132440681679, "serde_untagged", false, 5155161967588052656], [4071963112282141418, "serde_with", false, 17907700465971641528], [4899080583175475170, "semver", false, 1022521061288069304], [5986029879202738730, "log", false, 15157492001217083567], [6606131838865521726, "ctor", false, 14827647802088036809], [7170110829644101142, "json_patch", false, 1667207969227954341], [8319709847752024821, "uuid", false, 8845339123640000090], [9010263965687315507, "http", false, 5565084897169349038], [9090328626728818999, "toml", false, 16648950194795105859], [9451456094439810778, "regex", false, 11711502872458725713], [9556762810601084293, "brotli", false, 13428900807559770290], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [11989259058781683633, "dunce", false, 16890574421636915214], [13625485746686963219, "anyhow", false, 11831213235992925325], [15622660310229662834, "walkdir", false, 18195973706949392275], [15932120279885307830, "memchr", false, 5431602327195066064], [16362055519698394275, "serde_json", false, 9587711650641356174], [17146114186171651583, "infer", false, 2763266897982285769], [17155886227862585100, "glob", false, 9924612943057190799], [17186037756130803222, "phf", false, 888163844517894857]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-55a8787ea1773923/dep-lib-tauri_utils", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}