{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 14253890914074675322, "profile": 2330448797067240312, "path": 4942398508502643691, "deps": [[1797035611096599003, "tauri_plugin_clipboard_manager", false, 7440954348603013988], [2784153353110520258, "tauri_plugin_opener", false, 228482541069471858], [4972584477725338812, "tauri_plugin_shell", false, 15823165502371016992], [6825153089788476225, "tauri_plugin_dialog", false, 13744525416925419875], [9689903380558560274, "serde", false, 5024751070800530287], [9897246384292347999, "chrono", false, 15372099971842295566], [11996286768261087171, "screenshots", false, 11748749992922651137], [12092653563678505622, "tauri", false, 1392717550573660009], [12504415026414629397, "tauri_plugin_fs", false, 13712444315251039690], [13028763805764736075, "image", false, 17310633621944273387], [16362055519698394275, "serde_json", false, 4145907069708839085], [17531218394775549125, "tokio", false, 16336850265691007345], [17968271835982830072, "tauri_app_lib", false, 16700078774567042096], [17968271835982830072, "build_script_build", false, 2452907079922117859]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-app-8b5529bbd776e2c1/dep-bin-tauri-app", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}