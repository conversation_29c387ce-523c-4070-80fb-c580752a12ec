{"rustc": 12610991425282158916, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 3543196331859982759, "path": 5489715720310441358, "deps": [[309970253587158206, "block2", false, 4080211994986458294], [1386409696764982933, "objc2", false, 7749202734869270151], [3150220818285335163, "url", false, 9822936431616254700], [3722963349756955755, "once_cell", false, 18227399200341093905], [4143744114649553716, "raw_window_handle", false, 15738003606520721122], [7606335748176206944, "dpi", false, 7560431577017277240], [9010263965687315507, "http", false, 5565084897169349038], [9141053277961803901, "build_script_build", false, 14416591904203673480], [9628989939628929789, "objc2_web_kit", false, 4706366715497345171], [9859211262912517217, "objc2_foundation", false, 109152754633739772], [10378802769730441691, "objc2_core_foundation", false, 10880815993758585967], [10575598148575346675, "objc2_app_kit", false, 15827260513591514639], [10806645703491011684, "thiserror", false, 17824822482301571217], [16727543399706004146, "cookie", false, 14256012197545430411]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wry-4b77b2ce13197d38/dep-lib-wry", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}