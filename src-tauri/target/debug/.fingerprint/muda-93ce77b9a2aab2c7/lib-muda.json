{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"gtk\", \"serde\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"gtk\", \"libxdo\", \"serde\"]", "target": 5140358902516826434, "profile": 5347358027863023418, "path": 498459535146585641, "deps": [[1386409696764982933, "objc2", false, 7749202734869270151], [3722963349756955755, "once_cell", false, 18227399200341093905], [7606335748176206944, "dpi", false, 7560431577017277240], [9689903380558560274, "serde", false, 5898123663339760085], [9727213718512686088, "crossbeam_channel", false, 10417967221711976817], [9859211262912517217, "objc2_foundation", false, 109152754633739772], [10378802769730441691, "objc2_core_foundation", false, 10880815993758585967], [10575598148575346675, "objc2_app_kit", false, 15827260513591514639], [10806645703491011684, "thiserror", false, 17824822482301571217], [12687914511023397207, "png", false, 16931666791721165135], [14532484442929614732, "keyboard_types", false, 6587571047580740747]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/muda-93ce77b9a2aab2c7/dep-lib-muda", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}