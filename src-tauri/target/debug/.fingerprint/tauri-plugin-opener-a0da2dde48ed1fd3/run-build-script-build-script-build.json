{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 11706863006745327606], [2784153353110520258, "build_script_build", false, 15326341007629226984]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-plugin-opener-a0da2dde48ed1fd3/output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}