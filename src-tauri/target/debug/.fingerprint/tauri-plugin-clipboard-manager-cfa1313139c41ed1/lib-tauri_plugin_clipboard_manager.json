{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 16794383176018920266, "profile": 5347358027863023418, "path": 2140362830912178528, "deps": [[1797035611096599003, "build_script_build", false, 15856914410397685683], [5986029879202738730, "log", false, 15157492001217083567], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [12092653563678505622, "tauri", false, 15100766813278038496], [16362055519698394275, "serde_json", false, 9587711650641356174], [17455546449572272057, "arboard", false, 16817282607066950998]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-clipboard-manager-cfa1313139c41ed1/dep-lib-tauri_plugin_clipboard_manager", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}