{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 16310419681089088132], [17968271835982830072, "build_script_build", false, 8918740786509248464], [1797035611096599003, "build_script_build", false, 11922770308178497017], [6825153089788476225, "build_script_build", false, 8307570471267125102], [12504415026414629397, "build_script_build", false, 2009372325331915414], [2784153353110520258, "build_script_build", false, 764905611322719708], [4972584477725338812, "build_script_build", false, 3220743263638932987]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-app-b188f4ce3a189966/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}