{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 6259266586173490703, "deps": [[654232091421095663, "tauri_utils", false, 3593168340760584540], [3150220818285335163, "url", false, 9822936431616254700], [4143744114649553716, "raw_window_handle", false, 15738003606520721122], [7606335748176206944, "dpi", false, 7560431577017277240], [9010263965687315507, "http", false, 5565084897169349038], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [12943761728066819757, "build_script_build", false, 452831204330529123], [16362055519698394275, "serde_json", false, 9587711650641356174], [16727543399706004146, "cookie", false, 14256012197545430411]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-71883a3ad86a69c2/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}