{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 10527023000015881029, "deps": [[4972584477725338812, "build_script_build", false, 5302387717839474758], [5986029879202738730, "log", false, 15157492001217083567], [9451456094439810778, "regex", false, 11711502872458725713], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [11337703028400419576, "os_pipe", false, 7497318189190714091], [12092653563678505622, "tauri", false, 15100766813278038496], [14564311161534545801, "encoding_rs", false, 7382386852931800427], [15722096100444777195, "shared_child", false, 11861430647258540730], [16192041687293812804, "open", false, 13257687368530810176], [16362055519698394275, "serde_json", false, 9587711650641356174], [17531218394775549125, "tokio", false, 9638556237810568513]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-f3606389e213f530/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}