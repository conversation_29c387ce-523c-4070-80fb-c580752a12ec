{"rustc": 12610991425282158916, "features": "[\"AppKitDefines\", \"AppKitErrors\", \"NSATSTypesetter\", \"NSAccessibility\", \"NSAccessibilityColor\", \"NSAccessibilityConstants\", \"NSAccessibilityCustomAction\", \"NSAccessibilityCustomRotor\", \"NSAccessibilityElement\", \"NSAccessibilityProtocols\", \"NSActionCell\", \"NSAdaptiveImageGlyph\", \"NSAffineTransform\", \"NSAlert\", \"NSAlignmentFeedbackFilter\", \"NSAnimation\", \"NSAnimationContext\", \"NSAppearance\", \"NSAppleScriptExtensions\", \"NSApplication\", \"NSApplicationScripting\", \"NSArrayController\", \"NSAttributedString\", \"NSBezierPath\", \"NSBitmapImageRep\", \"NSBox\", \"NSBrowser\", \"NSBrowserCell\", \"NSButton\", \"NSButtonCell\", \"NSButtonTouchBarItem\", \"NSCIImageRep\", \"NSCachedImageRep\", \"NSCandidateListTouchBarItem\", \"NSCell\", \"NSClickGestureRecognizer\", \"NSClipView\", \"NSCollectionView\", \"NSCollectionViewCompositionalLayout\", \"NSCollectionViewFlowLayout\", \"NSCollectionViewGridLayout\", \"NSCollectionViewLayout\", \"NSCollectionViewTransitionLayout\", \"NSColor\", \"NSColorList\", \"NSColorPanel\", \"NSColorPicker\", \"NSColorPickerTouchBarItem\", \"NSColorPicking\", \"NSColorSampler\", \"NSColorSpace\", \"NSColorWell\", \"NSComboBox\", \"NSComboBoxCell\", \"NSComboButton\", \"NSControl\", \"NSController\", \"NSCursor\", \"NSCustomImageRep\", \"NSCustomTouchBarItem\", \"NSDataAsset\", \"NSDatePicker\", \"NSDatePickerCell\", \"NSDictionaryController\", \"NSDiffableDataSource\", \"NSDirection\", \"NSDockTile\", \"NSDocument\", \"NSDocumentController\", \"NSDocumentScripting\", \"NSDragging\", \"NSDraggingItem\", \"NSDraggingSession\", \"NSDrawer\", \"NSEPSImageRep\", \"NSErrors\", \"NSEvent\", \"NSFilePromiseProvider\", \"NSFilePromiseReceiver\", \"NSFileWrapperExtensions\", \"NSFont\", \"NSFontAssetRequest\", \"NSFontCollection\", \"NSFontDescriptor\", \"NSFontManager\", \"NSFontPanel\", \"NSForm\", \"NSFormCell\", \"NSGestureRecognizer\", \"NSGlyphGenerator\", \"NSGlyphInfo\", \"NSGradient\", \"NSGraphics\", \"NSGraphicsContext\", \"NSGridView\", \"NSGroupTouchBarItem\", \"NSHapticFeedback\", \"NSHelpManager\", \"NSImage\", \"NSImageCell\", \"NSImageRep\", \"NSImageView\", \"NSInputManager\", \"NSInputServer\", \"NSInterfaceStyle\", \"NSItemProvider\", \"NSKeyValueBinding\", \"NSLayoutAnchor\", \"NSLayoutConstraint\", \"NSLayoutGuide\", \"NSLayoutManager\", \"NSLevelIndicator\", \"NSLevelIndicatorCell\", \"NSMagnificationGestureRecognizer\", \"NSMatrix\", \"NSMediaLibraryBrowserController\", \"NSMenu\", \"NSMenuItem\", \"NSMenuItemBadge\", \"NSMenuItemCell\", \"NSMenuToolbarItem\", \"NSMovie\", \"NSNib\", \"NSNibConnector\", \"NSNibControlConnector\", \"NSNibDeclarations\", \"NSNibLoading\", \"NSNibOutletConnector\", \"NSObjectController\", \"NSOpenGL\", \"NSOpenGLLayer\", \"NSOpenGLView\", \"NSOpenPanel\", \"NSOutlineView\", \"NSPDFImageRep\", \"NSPDFInfo\", \"NSPDFPanel\", \"NSPICTImageRep\", \"NSPageController\", \"NSPageLayout\", \"NSPanGestureRecognizer\", \"NSPanel\", \"NSParagraphStyle\", \"NSPasteboard\", \"NSPasteboardItem\", \"NSPathCell\", \"NSPathComponentCell\", \"NSPathControl\", \"NSPathControlItem\", \"NSPersistentDocument\", \"NSPickerTouchBarItem\", \"NSPopUpButton\", \"NSPopUpButtonCell\", \"NSPopover\", \"NSPopoverTouchBarItem\", \"NSPredicateEditor\", \"NSPredicateEditorRowTemplate\", \"NSPressGestureRecognizer\", \"NSPressureConfiguration\", \"NSPreviewRepresentingActivityItem\", \"NSPrintInfo\", \"NSPrintOperation\", \"NSPrintPanel\", \"NSPrinter\", \"NSProgressIndicator\", \"NSResponder\", \"NSRotationGestureRecognizer\", \"NSRuleEditor\", \"NSRulerMarker\", \"NSRulerView\", \"NSRunningApplication\", \"NSSavePanel\", \"NSScreen\", \"NSScrollView\", \"NSScroller\", \"NSScrubber\", \"NSScrubberItemView\", \"NSScrubberLayout\", \"NSSearchField\", \"NSSearchFieldCell\", \"NSSearchToolbarItem\", \"NSSecureTextField\", \"NSSegmentedCell\", \"NSSegmentedControl\", \"NSShadow\", \"NSSharingCollaborationModeRestriction\", \"NSSharingService\", \"NSSharingServicePickerToolbarItem\", \"NSSharingServicePickerTouchBarItem\", \"NSSlider\", \"NSSliderAccessory\", \"NSSliderCell\", \"NSSliderTouchBarItem\", \"NSSound\", \"NSSpeechRecognizer\", \"NSSpeechSynthesizer\", \"NSSpellChecker\", \"NSSpellProtocol\", \"NSSplitView\", \"NSSplitViewController\", \"NSSplitViewItem\", \"NSStackView\", \"NSStatusBar\", \"NSStatusBarButton\", \"NSStatusItem\", \"NSStepper\", \"NSStepperCell\", \"NSStepperTouchBarItem\", \"NSStoryboard\", \"NSStoryboardSegue\", \"NSStringDrawing\", \"NSSwitch\", \"NSTabView\", \"NSTabViewController\", \"NSTabViewItem\", \"NSTableCellView\", \"NSTableColumn\", \"NSTableHeaderCell\", \"NSTableHeaderView\", \"NSTableRowView\", \"NSTableView\", \"NSTableViewDiffableDataSource\", \"NSTableViewRowAction\", \"NSText\", \"NSTextAlternatives\", \"NSTextAttachment\", \"NSTextAttachmentCell\", \"NSTextCheckingClient\", \"NSTextCheckingController\", \"NSTextContainer\", \"NSTextContent\", \"NSTextContentManager\", \"NSTextElement\", \"NSTextField\", \"NSTextFieldCell\", \"NSTextFinder\", \"NSTextInputClient\", \"NSTextInputContext\", \"NSTextInsertionIndicator\", \"NSTextLayoutFragment\", \"NSTextLayoutManager\", \"NSTextLineFragment\", \"NSTextList\", \"NSTextListElement\", \"NSTextRange\", \"NSTextSelection\", \"NSTextSelectionNavigation\", \"NSTextStorage\", \"NSTextStorageScripting\", \"NSTextTable\", \"NSTextView\", \"NSTextViewportLayoutController\", \"NSTintConfiguration\", \"NSTitlebarAccessoryViewController\", \"NSTokenField\", \"NSTokenFieldCell\", \"NSToolbar\", \"NSToolbarItem\", \"NSToolbarItemGroup\", \"NSTouch\", \"NSTouchBar\", \"NSTouchBarItem\", \"NSTrackingArea\", \"NSTrackingSeparatorToolbarItem\", \"NSTreeController\", \"NSTreeNode\", \"NSTypesetter\", \"NSUserActivity\", \"NSUserDefaultsController\", \"NSUserInterfaceCompression\", \"NSUserInterfaceItemIdentification\", \"NSUserInterfaceItemSearching\", \"NSUserInterfaceLayout\", \"NSUserInterfaceValidation\", \"NSView\", \"NSViewController\", \"NSVisualEffectView\", \"NSWindow\", \"NSWindowController\", \"NSWindowRestoration\", \"NSWindowScripting\", \"NSWindowTab\", \"NSWindowTabGroup\", \"NSWorkspace\", \"NSWritingToolsCoordinator\", \"NSWritingToolsCoordinatorAnimationParameters\", \"NSWritingToolsCoordinatorContext\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"libc\", \"objc2-cloud-kit\", \"objc2-core-data\", \"objc2-core-foundation\", \"objc2-core-graphics\", \"objc2-core-image\", \"objc2-quartz-core\", \"std\"]", "declared_features": "[\"AppKitDefines\", \"AppKitErrors\", \"NSATSTypesetter\", \"NSAccessibility\", \"NSAccessibilityColor\", \"NSAccessibilityConstants\", \"NSAccessibilityCustomAction\", \"NSAccessibilityCustomRotor\", \"NSAccessibilityElement\", \"NSAccessibilityProtocols\", \"NSActionCell\", \"NSAdaptiveImageGlyph\", \"NSAffineTransform\", \"NSAlert\", \"NSAlignmentFeedbackFilter\", \"NSAnimation\", \"NSAnimationContext\", \"NSAppearance\", \"NSAppleScriptExtensions\", \"NSApplication\", \"NSApplicationScripting\", \"NSArrayController\", \"NSAttributedString\", \"NSBezierPath\", \"NSBitmapImageRep\", \"NSBox\", \"NSBrowser\", \"NSBrowserCell\", \"NSButton\", \"NSButtonCell\", \"NSButtonTouchBarItem\", \"NSCIImageRep\", \"NSCachedImageRep\", \"NSCandidateListTouchBarItem\", \"NSCell\", \"NSClickGestureRecognizer\", \"NSClipView\", \"NSCollectionView\", \"NSCollectionViewCompositionalLayout\", \"NSCollectionViewFlowLayout\", \"NSCollectionViewGridLayout\", \"NSCollectionViewLayout\", \"NSCollectionViewTransitionLayout\", \"NSColor\", \"NSColorList\", \"NSColorPanel\", \"NSColorPicker\", \"NSColorPickerTouchBarItem\", \"NSColorPicking\", \"NSColorSampler\", \"NSColorSpace\", \"NSColorWell\", \"NSComboBox\", \"NSComboBoxCell\", \"NSComboButton\", \"NSControl\", \"NSController\", \"NSCursor\", \"NSCustomImageRep\", \"NSCustomTouchBarItem\", \"NSDataAsset\", \"NSDatePicker\", \"NSDatePickerCell\", \"NSDictionaryController\", \"NSDiffableDataSource\", \"NSDirection\", \"NSDockTile\", \"NSDocument\", \"NSDocumentController\", \"NSDocumentScripting\", \"NSDragging\", \"NSDraggingItem\", \"NSDraggingSession\", \"NSDrawer\", \"NSEPSImageRep\", \"NSErrors\", \"NSEvent\", \"NSFilePromiseProvider\", \"NSFilePromiseReceiver\", \"NSFileWrapperExtensions\", \"NSFont\", \"NSFontAssetRequest\", \"NSFontCollection\", \"NSFontDescriptor\", \"NSFontManager\", \"NSFontPanel\", \"NSForm\", \"NSFormCell\", \"NSGestureRecognizer\", \"NSGlyphGenerator\", \"NSGlyphInfo\", \"NSGradient\", \"NSGraphics\", \"NSGraphicsContext\", \"NSGridView\", \"NSGroupTouchBarItem\", \"NSHapticFeedback\", \"NSHelpManager\", \"NSImage\", \"NSImageCell\", \"NSImageRep\", \"NSImageView\", \"NSInputManager\", \"NSInputServer\", \"NSInterfaceStyle\", \"NSItemProvider\", \"NSKeyValueBinding\", \"NSLayoutAnchor\", \"NSLayoutConstraint\", \"NSLayoutGuide\", \"NSLayoutManager\", \"NSLevelIndicator\", \"NSLevelIndicatorCell\", \"NSMagnificationGestureRecognizer\", \"NSMatrix\", \"NSMediaLibraryBrowserController\", \"NSMenu\", \"NSMenuItem\", \"NSMenuItemBadge\", \"NSMenuItemCell\", \"NSMenuToolbarItem\", \"NSMovie\", \"NSNib\", \"NSNibConnector\", \"NSNibControlConnector\", \"NSNibDeclarations\", \"NSNibLoading\", \"NSNibOutletConnector\", \"NSObjectController\", \"NSOpenGL\", \"NSOpenGLLayer\", \"NSOpenGLView\", \"NSOpenPanel\", \"NSOutlineView\", \"NSPDFImageRep\", \"NSPDFInfo\", \"NSPDFPanel\", \"NSPICTImageRep\", \"NSPageController\", \"NSPageLayout\", \"NSPanGestureRecognizer\", \"NSPanel\", \"NSParagraphStyle\", \"NSPasteboard\", \"NSPasteboardItem\", \"NSPathCell\", \"NSPathComponentCell\", \"NSPathControl\", \"NSPathControlItem\", \"NSPersistentDocument\", \"NSPickerTouchBarItem\", \"NSPopUpButton\", \"NSPopUpButtonCell\", \"NSPopover\", \"NSPopoverTouchBarItem\", \"NSPredicateEditor\", \"NSPredicateEditorRowTemplate\", \"NSPressGestureRecognizer\", \"NSPressureConfiguration\", \"NSPreviewRepresentingActivityItem\", \"NSPrintInfo\", \"NSPrintOperation\", \"NSPrintPanel\", \"NSPrinter\", \"NSProgressIndicator\", \"NSResponder\", \"NSRotationGestureRecognizer\", \"NSRuleEditor\", \"NSRulerMarker\", \"NSRulerView\", \"NSRunningApplication\", \"NSSavePanel\", \"NSScreen\", \"NSScrollView\", \"NSScroller\", \"NSScrubber\", \"NSScrubberItemView\", \"NSScrubberLayout\", \"NSSearchField\", \"NSSearchFieldCell\", \"NSSearchToolbarItem\", \"NSSecureTextField\", \"NSSegmentedCell\", \"NSSegmentedControl\", \"NSShadow\", \"NSSharingCollaborationModeRestriction\", \"NSSharingService\", \"NSSharingServicePickerToolbarItem\", \"NSSharingServicePickerTouchBarItem\", \"NSSlider\", \"NSSliderAccessory\", \"NSSliderCell\", \"NSSliderTouchBarItem\", \"NSSound\", \"NSSpeechRecognizer\", \"NSSpeechSynthesizer\", \"NSSpellChecker\", \"NSSpellProtocol\", \"NSSplitView\", \"NSSplitViewController\", \"NSSplitViewItem\", \"NSStackView\", \"NSStatusBar\", \"NSStatusBarButton\", \"NSStatusItem\", \"NSStepper\", \"NSStepperCell\", \"NSStepperTouchBarItem\", \"NSStoryboard\", \"NSStoryboardSegue\", \"NSStringDrawing\", \"NSSwitch\", \"NSTabView\", \"NSTabViewController\", \"NSTabViewItem\", \"NSTableCellView\", \"NSTableColumn\", \"NSTableHeaderCell\", \"NSTableHeaderView\", \"NSTableRowView\", \"NSTableView\", \"NSTableViewDiffableDataSource\", \"NSTableViewRowAction\", \"NSText\", \"NSTextAlternatives\", \"NSTextAttachment\", \"NSTextAttachmentCell\", \"NSTextCheckingClient\", \"NSTextCheckingController\", \"NSTextContainer\", \"NSTextContent\", \"NSTextContentManager\", \"NSTextElement\", \"NSTextField\", \"NSTextFieldCell\", \"NSTextFinder\", \"NSTextInputClient\", \"NSTextInputContext\", \"NSTextInsertionIndicator\", \"NSTextLayoutFragment\", \"NSTextLayoutManager\", \"NSTextLineFragment\", \"NSTextList\", \"NSTextListElement\", \"NSTextRange\", \"NSTextSelection\", \"NSTextSelectionNavigation\", \"NSTextStorage\", \"NSTextStorageScripting\", \"NSTextTable\", \"NSTextView\", \"NSTextViewportLayoutController\", \"NSTintConfiguration\", \"NSTitlebarAccessoryViewController\", \"NSTokenField\", \"NSTokenFieldCell\", \"NSToolbar\", \"NSToolbarItem\", \"NSToolbarItemGroup\", \"NSTouch\", \"NSTouchBar\", \"NSTouchBarItem\", \"NSTrackingArea\", \"NSTrackingSeparatorToolbarItem\", \"NSTreeController\", \"NSTreeNode\", \"NSTypesetter\", \"NSUserActivity\", \"NSUserDefaultsController\", \"NSUserInterfaceCompression\", \"NSUserInterfaceItemIdentification\", \"NSUserInterfaceItemSearching\", \"NSUserInterfaceLayout\", \"NSUserInterfaceValidation\", \"NSView\", \"NSViewController\", \"NSVisualEffectView\", \"NSWindow\", \"NSWindowController\", \"NSWindowRestoration\", \"NSWindowScripting\", \"NSWindowTab\", \"NSWindowTabGroup\", \"NSWorkspace\", \"NSWritingToolsCoordinator\", \"NSWritingToolsCoordinatorAnimationParameters\", \"NSWritingToolsCoordinatorContext\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"libc\", \"objc2-cloud-kit\", \"objc2-core-data\", \"objc2-core-foundation\", \"objc2-core-graphics\", \"objc2-core-image\", \"objc2-quartz-core\", \"objc2-uniform-type-identifiers\", \"std\"]", "target": 18041863106907093401, "profile": 8196097686603091492, "path": 6322307089114808825, "deps": [[309970253587158206, "block2", false, 4080211994986458294], [1386409696764982933, "objc2", false, 7749202734869270151], [1517964206604611491, "objc2_quartz_core", false, 149680735634598123], [4684437522915235464, "libc", false, 5094650094651832587], [7828294911682607782, "objc2_core_graphics", false, 5941361144003572796], [7896293946984509699, "bitflags", false, 14456110716423544456], [9859211262912517217, "objc2_foundation", false, 109152754633739772], [10378802769730441691, "objc2_core_foundation", false, 10880815993758585967], [14310139767743755709, "objc2_core_image", false, 18343651117594617760], [15131121180101279514, "objc2_core_data", false, 6033217385479511812], [15796387676521131728, "objc2_cloud_kit", false, 11557651368999597562]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-app-kit-921176b88ed72ff1/dep-lib-objc2_app_kit", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}