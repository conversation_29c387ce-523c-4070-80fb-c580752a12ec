{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 11706863006745327606], [1797035611096599003, "build_script_build", false, 7682676760685266518]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-plugin-clipboard-manager-ea0f661d945b90aa/output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}