{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 2998846516364011741, "profile": 5347358027863023418, "path": 11318361432577717655, "deps": [[1454569275672942161, "objc2_app_kit", false, 5409763760017793206], [2784153353110520258, "build_script_build", false, 12405434528871735444], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [12092653563678505622, "tauri", false, 15100766813278038496], [15152681244819319733, "objc2_foundation", false, 8388509191381819393], [16192041687293812804, "open", false, 13257687368530810176], [16362055519698394275, "serde_json", false, 9587711650641356174], [17155886227862585100, "glob", false, 9924612943057190799]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-opener-8de6cb17c3c5c920/dep-lib-tauri_plugin_opener", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}