{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 6641054993558533036, "profile": 5347358027863023418, "path": 4034430591426836661, "deps": [[1672697574805174377, "rfd", false, 12295094592006575940], [3150220818285335163, "url", false, 9822936431616254700], [4143744114649553716, "raw_window_handle", false, 15738003606520721122], [5986029879202738730, "log", false, 15157492001217083567], [6825153089788476225, "build_script_build", false, 16864425342996309454], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [12092653563678505622, "tauri", false, 15100766813278038496], [12504415026414629397, "tauri_plugin_fs", false, 3327201831565337115], [16362055519698394275, "serde_json", false, 9587711650641356174]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-dialog-99e501c1a68aafbb/dep-lib-tauri_plugin_dialog", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}