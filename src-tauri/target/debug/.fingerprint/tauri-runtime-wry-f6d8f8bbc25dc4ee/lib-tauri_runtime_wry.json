{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 870312067241413047, "deps": [[654232091421095663, "tauri_utils", false, 3593168340760584540], [1386409696764982933, "objc2", false, 7749202734869270151], [3150220818285335163, "url", false, 9822936431616254700], [4143744114649553716, "raw_window_handle", false, 15738003606520721122], [5986029879202738730, "log", false, 15157492001217083567], [8826339825490770380, "tao", false, 12794977770771682093], [9010263965687315507, "http", false, 5565084897169349038], [9141053277961803901, "wry", false, 17263670517959726334], [9859211262912517217, "objc2_foundation", false, 109152754633739772], [10575598148575346675, "objc2_app_kit", false, 15827260513591514639], [12304025191202589669, "build_script_build", false, 13439049596196659231], [12943761728066819757, "tauri_runtime", false, 10346589266412806953]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-f6d8f8bbc25dc4ee/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}