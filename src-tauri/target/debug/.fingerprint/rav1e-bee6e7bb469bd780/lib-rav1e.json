{"rustc": 12610991425282158916, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 5347358027863023418, "path": 12913972129179795890, "deps": [[2687729594444538932, "debug_unreachable", false, 13170551940838354258], [2828590642173593838, "cfg_if", false, 524302589803563590], [3722963349756955755, "once_cell", false, 18227399200341093905], [4684437522915235464, "libc", false, 5094650094651832587], [5157631553186200874, "num_traits", false, 2621188795084415729], [5237962722597217121, "simd_helpers", false, 5783509068194192814], [5626665093607998638, "build_script_build", false, 9460918474682305295], [5986029879202738730, "log", false, 15157492001217083567], [6697151524989202978, "profiling", false, 4471505981722653430], [7074416887430417773, "av1_grain", false, 8388677360709246777], [8008191657135824715, "thiserror", false, 17414498487205770219], [11263754829263059703, "num_derive", false, 6797020712153697524], [12672448913558545127, "noop_proc_macro", false, 5634528065259397564], [13847662864258534762, "arrayvec", false, 8014324750581900342], [14931062873021150766, "itertools", false, 13253405067291439289], [15325537792103828505, "v_frame", false, 1299203902885087201], [16507960196461048755, "rayon", false, 6585333896066793747], [17605717126308396068, "paste", false, 10449321787564625303], [17706129463675219700, "arg_enum_proc_macro", false, 5743656277940614176], [17933778289016427379, "bitstream_io", false, 17855765558410646835]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rav1e-bee6e7bb469bd780/dep-lib-rav1e", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}