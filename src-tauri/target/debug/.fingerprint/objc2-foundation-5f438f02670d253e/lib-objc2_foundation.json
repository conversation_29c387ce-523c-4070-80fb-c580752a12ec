{"rustc": 12610991425282158916, "features": "[\"NSAppleEventDescriptor\", \"NSArray\", \"NSAttributedString\", \"NSCoder\", \"NSData\", \"NSDate\", \"NSDictionary\", \"NSEnumerator\", \"NSError\", \"NSException\", \"NSFileManager\", \"NSFormatter\", \"NSGeometry\", \"NSNotification\", \"NSObjCRuntime\", \"NSObject\", \"NSRange\", \"NSString\", \"NSThread\", \"NSURL\", \"NSUndoManager\", \"NSUserActivity\", \"NSValue\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"dispatch\", \"std\"]", "declared_features": "[\"FoundationErrors\", \"FoundationLegacySwiftCompatibility\", \"NSAffineTransform\", \"NSAppleEventDescriptor\", \"NSAppleEventManager\", \"NSAppleScript\", \"NSArchiver\", \"NSArray\", \"NSAttributedString\", \"NSAutoreleasePool\", \"NSBackgroundActivityScheduler\", \"NSBundle\", \"NSByteCountFormatter\", \"NSByteOrder\", \"NSCache\", \"NSCalendar\", \"NSCalendarDate\", \"NSCharacterSet\", \"NSClassDescription\", \"NSCoder\", \"NSComparisonPredicate\", \"NSCompoundPredicate\", \"NSConnection\", \"NSData\", \"NSDate\", \"NSDateComponentsFormatter\", \"NSDateFormatter\", \"NSDateInterval\", \"NSDateIntervalFormatter\", \"NSDecimal\", \"NSDecimalNumber\", \"NSDictionary\", \"NSDistantObject\", \"NSDistributedLock\", \"NSDistributedNotificationCenter\", \"NSEnergyFormatter\", \"NSEnumerator\", \"NSError\", \"NSException\", \"NSExpression\", \"NSExtensionContext\", \"NSExtensionItem\", \"NSExtensionRequestHandling\", \"NSFileCoordinator\", \"NSFileHandle\", \"NSFileManager\", \"NSFilePresenter\", \"NSFileVersion\", \"NSFileWrapper\", \"NSFormatter\", \"NSGarbageCollector\", \"NSGeometry\", \"NSHFSFileTypes\", \"NSHTTPCookie\", \"NSHTTPCookieStorage\", \"NSHashTable\", \"NSHost\", \"NSISO8601DateFormatter\", \"NSIndexPath\", \"NSIndexSet\", \"NSInflectionRule\", \"NSInvocation\", \"NSItemProvider\", \"NSJSONSerialization\", \"NSKeyValueCoding\", \"NSKeyValueObserving\", \"NSKeyedArchiver\", \"NSLengthFormatter\", \"NSLinguisticTagger\", \"NSListFormatter\", \"NSLocale\", \"NSLock\", \"NSMapTable\", \"NSMassFormatter\", \"NSMeasurement\", \"NSMeasurementFormatter\", \"NSMetadata\", \"NSMetadataAttributes\", \"NSMethodSignature\", \"NSMorphology\", \"NSNetServices\", \"NSNotification\", \"NSNotificationQueue\", \"NSNull\", \"NSNumberFormatter\", \"NSObjCRuntime\", \"NSObject\", \"NSObjectScripting\", \"NSOperation\", \"NSOrderedCollectionChange\", \"NSOrderedCollectionDifference\", \"NSOrderedSet\", \"NSOrthography\", \"NSPathUtilities\", \"NSPersonNameComponents\", \"NSPersonNameComponentsFormatter\", \"NSPointerArray\", \"NSPointerFunctions\", \"NSPort\", \"NSPortCoder\", \"NSPortMessage\", \"NSPortNameServer\", \"NSPredicate\", \"NSProcessInfo\", \"NSProgress\", \"NSPropertyList\", \"NSProtocolChecker\", \"NSProxy\", \"NSRange\", \"NSRegularExpression\", \"NSRelativeDateTimeFormatter\", \"NSRunLoop\", \"NSScanner\", \"NSScriptClassDescription\", \"NSScriptCoercionHandler\", \"NSScriptCommand\", \"NSScriptCommandDescription\", \"NSScriptExecutionContext\", \"NSScriptKeyValueCoding\", \"NSScriptObjectSpecifiers\", \"NSScriptStandardSuiteCommands\", \"NSScriptSuiteRegistry\", \"NSScriptWhoseTests\", \"NSSet\", \"NSSortDescriptor\", \"NSSpellServer\", \"NSStream\", \"NSString\", \"NSTask\", \"NSTermOfAddress\", \"NSTextCheckingResult\", \"NSThread\", \"NSTimeZone\", \"NSTimer\", \"NSURL\", \"NSURLAuthenticationChallenge\", \"NSURLCache\", \"NSURLConnection\", \"NSURLCredential\", \"NSURLCredentialStorage\", \"NSURLDownload\", \"NSURLError\", \"NSURLHandle\", \"NSURLProtectionSpace\", \"NSURLProtocol\", \"NSURLRequest\", \"NSURLResponse\", \"NSURLSession\", \"NSUUID\", \"NSUbiquitousKeyValueStore\", \"NSUndoManager\", \"NSUnit\", \"NSUserActivity\", \"NSUserDefaults\", \"NSUserNotification\", \"NSUserScriptTask\", \"NSValue\", \"NSValueTransformer\", \"NSXMLDTD\", \"NSXMLDTDNode\", \"NSXMLDocument\", \"NSXMLElement\", \"NSXMLNode\", \"NSXMLNodeOptions\", \"NSXMLParser\", \"NSXPCConnection\", \"NSZone\", \"all\", \"alloc\", \"apple\", \"bitflags\", \"block2\", \"default\", \"dispatch\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"libc\", \"std\", \"unstable-static-nsstring\"]", "target": 12262679844544453048, "profile": 6305132673592726323, "path": 6901828824284591728, "deps": [[4917092332511929119, "block2", false, 8935027087404778465], [7896293946984509699, "bitflags", false, 14456110716423544456], [12421151999272876257, "objc2", false, 1077130595817827942], [16431863889745914764, "dispatch", false, 12666230932291648353]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-foundation-5f438f02670d253e/dep-lib-objc2_foundation", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}