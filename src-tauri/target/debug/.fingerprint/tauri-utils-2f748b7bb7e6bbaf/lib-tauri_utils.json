{"rustc": 12610991425282158916, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 3033921117576893, "path": 69793902400042306, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 14260845937334357086], [3060637413840920116, "proc_macro2", false, 5643294594616372651], [3150220818285335163, "url", false, 1678743514544208927], [3191507132440681679, "serde_untagged", false, 12892862605190212151], [4071963112282141418, "serde_with", false, 10600775534668152957], [4899080583175475170, "semver", false, 16697132971021793852], [5986029879202738730, "log", false, 15157492001217083567], [6606131838865521726, "ctor", false, 14827647802088036809], [6913375703034175521, "schemars", false, 6420167350046046162], [7170110829644101142, "json_patch", false, 17597047808237197697], [8319709847752024821, "uuid", false, 6849172894290933829], [9010263965687315507, "http", false, 5565084897169349038], [9090328626728818999, "toml", false, 10981269422323874532], [9451456094439810778, "regex", false, 11711502872458725713], [9556762810601084293, "brotli", false, 13428900807559770290], [9689903380558560274, "serde", false, 9410213884670843148], [10806645703491011684, "thiserror", false, 17824822482301571217], [11655476559277113544, "cargo_metadata", false, 1378496902284875263], [11989259058781683633, "dunce", false, 16890574421636915214], [13625485746686963219, "anyhow", false, 11831213235992925325], [14232843520438415263, "html5ever", false, 16512732373371436698], [14885200901422974105, "swift_rs", false, 2136686522963218435], [15088007382495681292, "kuchiki", false, 10084974158492669526], [15622660310229662834, "walkdir", false, 18195973706949392275], [15932120279885307830, "memchr", false, 5431602327195066064], [16362055519698394275, "serde_json", false, 3713609091296805696], [17146114186171651583, "infer", false, 10618030775426530345], [17155886227862585100, "glob", false, 9924612943057190799], [17186037756130803222, "phf", false, 18443474354241221723], [17990358020177143287, "quote", false, 5791034238459340517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-2f748b7bb7e6bbaf/dep-lib-tauri_utils", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}