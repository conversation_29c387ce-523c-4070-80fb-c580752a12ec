{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 11706863006745327606], [4972584477725338812, "build_script_build", false, 15041045162915835060]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-plugin-shell-a033c8637acbe539/output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}