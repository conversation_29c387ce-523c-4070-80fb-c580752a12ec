{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 13022821288631839670, "deps": [[40386456601120721, "percent_encoding", false, 17342364992837630480], [654232091421095663, "tauri_utils", false, 3593168340760584540], [1200537532907108615, "url<PERSON><PERSON>n", false, 9282436027256527153], [1386409696764982933, "objc2", false, 7749202734869270151], [1967864351173319501, "muda", false, 10653313667238590118], [3150220818285335163, "url", false, 9822936431616254700], [3331586631144870129, "getrandom", false, 8107457147187312773], [4143744114649553716, "raw_window_handle", false, 15738003606520721122], [4919829919303820331, "serialize_to_javascript", false, 12989618516070455155], [5986029879202738730, "log", false, 15157492001217083567], [8589231650440095114, "embed_plist", false, 4663182608340373787], [9010263965687315507, "http", false, 5565084897169349038], [9689903380558560274, "serde", false, 5898123663339760085], [9859211262912517217, "objc2_foundation", false, 109152754633739772], [10229185211513642314, "mime", false, 15317600359243473040], [10575598148575346675, "objc2_app_kit", false, 15827260513591514639], [10806645703491011684, "thiserror", false, 17824822482301571217], [11989259058781683633, "dunce", false, 16890574421636915214], [12092653563678505622, "build_script_build", false, 11706863006745327606], [12304025191202589669, "tauri_runtime_wry", false, 7241533919861690990], [12565293087094287914, "window_vibrancy", false, 17640231487105699445], [12943761728066819757, "tauri_runtime", false, 10346589266412806953], [12986574360607194341, "serde_repr", false, 7283249900449093536], [13077543566650298139, "heck", false, 6788684706634904386], [13405681745520956630, "tauri_macros", false, 3487410749716656689], [13625485746686963219, "anyhow", false, 11831213235992925325], [14687315689416489882, "plist", false, 9824300326980115942], [16362055519698394275, "serde_json", false, 9587711650641356174], [16928111194414003569, "dirs", false, 3950943480248490685], [17155886227862585100, "glob", false, 9924612943057190799], [17531218394775549125, "tokio", false, 9638556237810568513]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-4dac9dff2dc6d3f4/dep-lib-tauri", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}