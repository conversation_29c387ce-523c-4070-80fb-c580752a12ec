{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"notify\", \"notify-debouncer-full\", \"watch\"]", "target": 617784021486520941, "profile": 5347358027863023418, "path": 563534133598770639, "deps": [[40386456601120721, "percent_encoding", false, 17342364992837630480], [3150220818285335163, "url", false, 9822936431616254700], [9689903380558560274, "serde", false, 5898123663339760085], [10806645703491011684, "thiserror", false, 17824822482301571217], [11989259058781683633, "dunce", false, 16890574421636915214], [12092653563678505622, "tauri", false, 15100766813278038496], [12504415026414629397, "build_script_build", false, 18153911568556095711], [12986574360607194341, "serde_repr", false, 7283249900449093536], [13625485746686963219, "anyhow", false, 11831213235992925325], [16362055519698394275, "serde_json", false, 9587711650641356174], [17155886227862585100, "glob", false, 9924612943057190799]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-fs-6238662f37caa7f7/dep-lib-tauri_plugin_fs", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}