{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 11706863006745327606], [17968271835982830072, "build_script_build", false, 10733308965352081195], [1797035611096599003, "build_script_build", false, 15856914410397685683], [6825153089788476225, "build_script_build", false, 16864425342996309454], [12504415026414629397, "build_script_build", false, 18153911568556095711], [2784153353110520258, "build_script_build", false, 12405434528871735444], [4972584477725338812, "build_script_build", false, 5302387717839474758]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-app-a8aa0beeead3f93c/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}