{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"glib-sys\", \"gobject-sys\", \"gtk-sys\", \"gtk3\", \"tokio\"]", "declared_features": "[\"ashpd\", \"async-std\", \"common-controls-v6\", \"default\", \"file-handle-inner\", \"glib-sys\", \"gobject-sys\", \"gtk-sys\", \"gtk3\", \"pollster\", \"tokio\", \"urlencoding\", \"xdg-portal\"]", "target": 2038336923818351611, "profile": 5347358027863023418, "path": 12751498953422194401, "deps": [[1454569275672942161, "objc2_app_kit", false, 5409763760017793206], [1672697574805174377, "build_script_build", false, 2427079940317633983], [4143744114649553716, "raw_window_handle", false, 15738003606520721122], [4917092332511929119, "block2", false, 8935027087404778465], [5986029879202738730, "log", false, 15157492001217083567], [12421151999272876257, "objc2", false, 1077130595817827942], [15152681244819319733, "objc2_foundation", false, 8388509191381819393]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rfd-65f1f310c8848fdb/dep-lib-rfd", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}