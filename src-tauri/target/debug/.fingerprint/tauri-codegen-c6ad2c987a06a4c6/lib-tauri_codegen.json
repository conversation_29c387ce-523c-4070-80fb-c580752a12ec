{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 3033************, "path": 14691219624867597285, "deps": [[654232091421095663, "tauri_utils", false, 15120998868653369841], [3060637413840920116, "proc_macro2", false, 5643294594616372651], [3150220818285335163, "url", false, 1678743514544208927], [4899080583175475170, "semver", false, 16697132971021793852], [4974441333307933176, "syn", false, 2207489294094183346], [7170110829644101142, "json_patch", false, 17597047808237197697], [7392050791754369441, "ico", false, 14236153050728530870], [8319709847752024821, "uuid", false, 6849172894290933829], [9556762810601084293, "brotli", false, 13428900807559770290], [9689903380558560274, "serde", false, 9410213884670843148], [9857275760291862238, "sha2", false, 16945390812727046770], [10806645703491011684, "thiserror", false, 17824822482301571217], [12409575957772518135, "time", false, 18204998947202769152], [12687914511023397207, "png", false, 16931666791721165135], [13077212702700853852, "base64", false, 4509547063769670401], [14687315689416489882, "plist", false, 12605218423518414078], [15622660310229662834, "walkdir", false, 18195973706949392275], [16362055519698394275, "serde_json", false, 3713609091296805696], [17990358020177143287, "quote", false, 5791034238459340517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-c6ad2c987a06a4c6/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}