{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 14253890914074675322, "profile": 6675295047989516842, "path": 4942398508502643691, "deps": [[1797035611096599003, "tauri_plugin_clipboard_manager", false, 2282591277565223155], [2784153353110520258, "tauri_plugin_opener", false, 16710723074732090776], [4972584477725338812, "tauri_plugin_shell", false, 3070719470408800712], [6825153089788476225, "tauri_plugin_dialog", false, 13661396109966447569], [9689903380558560274, "serde", false, 5898123663339760085], [9897246384292347999, "chrono", false, 10639821575586301257], [11996286768261087171, "screenshots", false, 6014678390899986669], [12092653563678505622, "tauri", false, 15100766813278038496], [12504415026414629397, "tauri_plugin_fs", false, 3327201831565337115], [13028763805764736075, "image", false, 10905579209277167953], [16362055519698394275, "serde_json", false, 9587711650641356174], [17531218394775549125, "tokio", false, 9638556237810568513], [17968271835982830072, "tauri_app_lib", false, 7811342286466896390], [17968271835982830072, "build_script_build", false, 6624028570224093660]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-app-e47bcace8969deb7/dep-bin-tauri-app", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}